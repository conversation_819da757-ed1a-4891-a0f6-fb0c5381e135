# Throughput Data Processing - GUI pro Sampling Konformací Molekul

Program slouží k **samplingu konformací molekul na površích iontových krystalů** s možností analýzy výkonnosti různých výpočetních parametrů.

## 🎯 Účel Programu

Tento nástroj umožňuje:
- **Sampling molekulárních konformací** na površích iontových krystalů (např. NaCl)
- **Optimalizaci výpočetních parametrů** pro molekulární dynamiku
- **Analýzu výkonnosti** různých nastavení simulací
- **Generování dat** pro studium interakcí molekula-povrch

## 🚀 Spuštění GUI

```bash
python3 throughput_gui.py
```

## 📋 Hlavní Funkce GUI

### 1. **Záložka "Optimization"** - <PERSON><PERSON><PERSON><PERSON> simulace

#### Základní Parametry
- **dovdW**: Van der Waals interakce (0/1)
- **doSurfAtoms**: Použití atomů povrchu (0/1) 
- **bGridFF**: Grid Force Field (0=vypnuto, 1=lineární, 6=bSpline)
- **bTex**: Textura (0/1)
- **bSaveToDatabase**: Uložení do databáze (-1/0/1)

#### Cesty k Souborům
- **XYZ File**: Soubor s molekulou (např. `data/xyz/xylitol_WO_gridFF`)
- **Surface File**: Šablona pro povrchové soubory s `${N}` placeholder
  - Příklad: `data/xyz/surfaces_for_throughput/NaCl_${N}x${N}_L3`
  - `${N}` se automaticky nahradí velikostí povrchu (1x1, 4x4, 8x8, atd.)

#### Konvergenční Kritéria
- **Fconv**: Kritérium konvergence síly (např. 1e-4)

#### Pokročilé Parametry
- **Replicas**: Počet replik (např. "1000,5000")
- **Perframes**: Parametr per frame (např. "20,500") 
- **PerVF**: Parametr per VF (např. "20,50")
- **nPBC**: Periodické okrajové podmínky (např. "(1,1,0)")
- **Ns**: Velikosti povrchů (např. "1-16" nebo "1,4,8,16")

#### Lokální Paměťové Parametry
- **nlocMMFFs**: Lokální paměť pro MMFF
- **nlocmoves**: Lokální paměť pro moves
- **nlocNBFFs**: Lokální paměť pro NBFF
- **nlocSurfs**: Lokální paměť pro surface
- **nlocGridFFs**: Lokální paměť pro GridFF
- **nlocGridFFbSplines**: Lokální paměť pro GridFF bSplines

### 2. **Záložka "Visualization"** - Analýza Výsledků

- **Generování interaktivních grafů** výkonnosti
- **Zobrazení tabulky výsledků** 
- **Otevření složky s výsledky**
- **Automatické otevření v prohlížeči**

## 📁 Předpřipravené Konfigurace

### `params_surface_with_gridff.txt`
```
# Simulace s povrchem a Grid Force Field
doSurfAtoms=1
bGridFF=6
surf_name=data/xyz/surfaces_for_throughput/NaCl_${N}x${N}_L3
```

### `params_surface_no_gridff.txt`
```
# Simulace s povrchem bez Grid Force Field
doSurfAtoms=1
bGridFF=0
surf_name=data/xyz/surfaces_for_throughput/NaCl_${N}x${N}_L3
```

### `params_no_surface.txt`
```
# Simulace bez povrchu
doSurfAtoms=0
surf_name=
```

## 🔧 Použití

### 1. Načtení Přednastavení
```
File → Load Preset → Vyberte soubor (např. params_surface_with_gridff.txt)
```

### 2. Ruční Nastavení
1. Nastavte základní parametry podle typu simulace
2. Zadejte cesty k XYZ a surface souborům
3. Nastavte rozsahy parametrů pro testování
4. Spusťte optimalizaci

### 3. Analýza Výsledků
1. Přejděte na záložku "Visualization"
2. Vyberte soubor s výsledky
3. Generujte interaktivní grafy
4. Analyzujte výkonnostní data

## 📊 Výstupní Data

Program generuje:
- **minima.dat**: Výsledky jednotlivých simulací
- **results.dat**: Souhrnné výsledky všech běhů
- **results_table.csv**: Tabulka pro analýzu
- **Interaktivní HTML grafy**: Pro vizualizaci výkonnosti

## 🧪 Typy Simulací

### Bez Povrchu
- Studium volných molekul
- Baseline pro porovnání

### S Povrchem (bez GridFF)
- Klasické interakce molekula-povrch
- Pomalejší, ale přesnější

### S Povrchem (s GridFF)
- Rychlejší výpočty pomocí grid interpolace
- Vhodné pro velké systémy

## 💡 Tipy pro Použití

1. **Začněte s malými systémy** (N=1-4) pro testování
2. **Použijte GridFF** pro velké povrchy (N>8)
3. **Nastavte více replik** pro lepší statistiku
4. **Sledujte konvergenci** pomocí Fconv parametru
5. **Analyzujte výsledky** pomocí vizualizačních nástrojů

## 🔍 Troubleshooting

- **Chyba "minima.dat not found"**: Simulace selhala, zkontrolujte parametry
- **Pomalé výpočty**: Zvažte použití GridFF nebo menší systémy
- **Chybějící soubory**: Ověřte cesty k XYZ a surface souborům

## 📈 Výkonnostní Analýza

Program umožňuje analýzu:
- **Škálování s velikostí povrchu** (NxN závislost)
- **Vliv počtu replik** na přesnost
- **Optimální nastavení paměti** (nloc parametry)
- **Porovnání různých metod** (GridFF vs. klasické)
